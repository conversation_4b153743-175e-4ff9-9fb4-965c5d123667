# 🌐 Graph Visualizer

Interactive, responsive, and theme-aware **Graph Visualizer** built with **React**, **TailwindCSS**, and **D3.js**. Supports both **directed** and **undirected** graphs with powerful algorithms like **DFS**, **BFS**, **MST (<PERSON><PERSON>'s)**, and **SCC** – all visualized beautifully.

![Preview](https://github.com/user-attachments/assets/35270409-e97e-4341-addd-1d65b735c336)


---

## ✨ Features

- 🎯 **Directed / Undirected Graphs**
- 🧭 **DFS** & **BFS** traversal visualization
- 🧮 **Weighted Graphs** with **edge weights**
- 🌳 **Minimum Spanning Tree (Prim's Algorithm)**
- 🔁 **Strongly Connected Components (SCC)**
- 💫 **Euler path and circuit** 
- 🎨 **Responsive Design**
- 🌗 **Dark/Light Mode Toggle**
- 🧠 Smart edge drawing with **node-safe anchoring**
- 📱 Mobile-friendly and performant

---

## 🧪 Demo

[🔗 Live GitHub Page](https://niravpokiya.github.io/Network-graph-visualizer)

---

## 📦 Installation

```bash
# Clone this repo
git clone https://github.com/niravpokiya/Network-graph-visualizer.git

# Navigate to project
cd Network-graph-visualizer

# Install dependencies
npm install

# Start development server
npm run dev


