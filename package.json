{"name": "graph-visualizer", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://niravpokiya.github.io/Network-graph-visualizer", "scripts": {"predeploy": "npm run build", "deploy": "gh-pages -d dist", "dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@datastructures-js/priority-queue": "^6.3.3", "@tailwindcss/vite": "^4.1.10", "d3": "^7.9.0", "daisyui": "^5.0.43", "react": "^19.1.0", "react-dark-mode-toggle": "^0.2.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "gh-pages": "^6.3.0", "globals": "^16.0.0", "postcss": "^8.5.5", "tailwindcss": "^4.1.10", "vite": "^6.3.5", "vite-plugin-gh-pages": "^1.0.1"}}